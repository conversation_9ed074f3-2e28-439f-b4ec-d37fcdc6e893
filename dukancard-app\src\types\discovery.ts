/**
 * TypeScript types and interfaces for discovery functionality
 * Aligned with dukancard/app/(main)/discover/context/types.ts and dukancard/lib/actions/businessProfiles/types.ts
 */

import { BusinessProfiles } from "./database/business-profiles";
import { ProductsServices } from "./database/products-services";

export {
  ProductsServices as ProductData,
  BusinessProfiles as BusinessCardData,
} from "./database";

// Define sorting options for business profiles (from dukancard/lib/actions/businessProfiles/types.ts)
export type BusinessSortBy =
  | "name_asc"
  | "name_desc"
  | "created_asc"
  | "created_desc"
  | "likes_asc"
  | "likes_desc"
  | "subscriptions_asc"
  | "subscriptions_desc"
  | "rating_asc"
  | "rating_desc"
  | "price_asc"
  | "price_desc";

// Define the product sort options for the discovery page (from dukancard/app/(main)/discover/context/types.ts)
export type ProductSortOption =
  | "newest"
  | "price_low"
  | "price_high"
  | "name_asc"
  | "name_desc";

// Define the business sort options for the discovery page (from dukancard/app/(main)/discover/context/types.ts)
export type BusinessSortOption =
  | "newest"
  | "name_asc"
  | "name_desc"
  | "most_liked"
  | "most_subscribed"
  | "highest_rated";

// Define the product filter options (from dukancard/app/(main)/discover/context/types.ts)
export type ProductFilterOption = "all" | "physical" | "service";

// Define the view types (from dukancard/app/(main)/discover/context/types.ts)
export type ViewType = "cards" | "products";

// Define the structure for products found nearby (from dukancard/app/(main)/discover/actions/types.ts)
export type NearbyProduct = ProductsServices & {
  business_slug: string | null;
  category?: string;
  stock_quantity?: number;
  sku?: string;
  barcode?: string;
  tags?: string[];
  related_products?: string[];
  brand?: string;
  weight?: number;
  dimensions?: string;
  shipping_info?: string;
  availability_date?: string;
  is_featured?: boolean;
  tax_rate?: number;
  is_taxable?: boolean;
  purchase_limit?: number;
  is_on_sale?: boolean;
  sale_start_date?: string;
  sale_end_date?: string;
  requires_shipping?: boolean;
  is_digital?: boolean;
  download_url?: string;
  download_limit?: number;
  meta_title?: string;
  meta_description?: string;
  notes?: string;
  commission_rate?: number;
  is_active?: boolean;
  business_profile?: any;
  distance?: number;
  business_name?: string;
  business_logo_url?: string;
  businessLatitude?: number | null;
  businessLongitude?: number | null;
};

// Define the location structure (from dukancard-app/src/types/discovery.ts - existing)
export interface LocationData {
  city?: string;
  state?: string;
  pincode?: string;
  locality?: string;
  latitude?: number;
  longitude?: number;
}

// Define the search result structure (from dukancard/app/(main)/discover/context/types.ts)
export type DiscoverSearchResult = {
  location?: { city: string; state: string } | null; // Note: Next.js version has city/state here
  businesses?: BusinessProfiles[]; // Changed from BusinessCardData to BusinessProfiles
  products?: NearbyProduct[];
  isAuthenticated: boolean;
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
};

// Define the combined search form data (from dukancard/app/(main)/discover/context/types.ts)
export type CombinedSearchFormData = {
  businessName?: string | null;
  productName?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  category?: string | null;
};

// Define the context type (from dukancard/app/(main)/discover/context/types.ts, adapted for RN)
export type DiscoverContextType = {
  // State
  viewType: ViewType;
  sortBy: BusinessSortBy; // Changed from BusinessSortOption to BusinessSortBy
  isSearching: boolean;

  isLoadingMore: boolean;
  isSorting: boolean;
  isFilteringByCategory: boolean;
  searchError: string | null;
  productFilterBy: ProductFilterOption;
  productSortBy: ProductSortOption;
  searchResult: DiscoverSearchResult | null;
  businesses: BusinessProfiles[]; // Changed from BusinessCardData to BusinessProfiles
  products: NearbyProduct[];
  currentPage: number;
  hasMore: boolean;
  totalCount: number;
  isAuthenticated: boolean;
  searchTerm: string; // Added searchTerm
  userLocation: LocationData | null; // Added userLocation
  isLocationLoading: boolean; // Added isLocationLoading
  selectedCategory: string | null; // Added selectedCategory

  // Functions
  performSearch: (data: CombinedSearchFormData) => void;
  handleViewChange: (view: ViewType) => void;
  handleBusinessSortChange: (sortOption: BusinessSortBy) => void; // Changed from BusinessSortOption to BusinessSortBy
  handleSearch: (term: string) => void; // Added handleSearch
  handleClearSearch: () => void; // Added clear search functionality
  handleProductSortChange: (sortOption: ProductSortOption) => void;
  handleProductFilterChange: (filter: ProductFilterOption) => void;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>; // Added refresh
  updateLocation: (location: LocationData) => Promise<void>; // Added updateLocation
  handleCategoryChange: (category: string | null) => void; // Added handleCategoryChange
};

// Discovery service response types (simplified to match Next.js server action return)
export interface DiscoveryServiceResponse<T = any> {
  data?: T;
  error?: string;
}

// Helper function to get the column name for sorting (adapted from dukancard/app/(main)/discover/utils/sortMappings.ts)
export function getSortingColumn(
  sortBy: BusinessSortBy | ProductSortOption | string
): string {
  // Extract the column name from the sort option (e.g., "created_desc" -> "created_at")
  const parts = sortBy.split("_");
  if (parts.length < 2) return "created_at";

  // Special cases
  if (parts[0] === "likes") return "total_likes";
  if (parts[0] === "subscriptions") return "total_subscriptions";
  if (parts[0] === "rating") return "average_rating";
  if (parts[0] === "newest") return "created_at"; // Handle 'newest' as a special case

  // Standard cases
  if (parts[0] === "created") return "created_at";
  if (parts[0] === "name") return "name"; // For both business and product names
  if (parts[0] === "price") return "base_price";

  return parts[0];
}

// Helper function to determine sort direction (adapted from dukancard/app/(main)/discover/utils/sortMappings.ts)
export function getSortingDirection(
  sortBy: BusinessSortBy | ProductSortOption | string
): boolean {
  // Extract the direction from the sort option (e.g., "created_desc" -> false for descending)
  return !sortBy.endsWith("_desc") && !sortBy.endsWith("_high");
}
